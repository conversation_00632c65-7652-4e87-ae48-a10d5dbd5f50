import { <PERSON>mpo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hild, ElementRef } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { FlcDrawerHelperService, FlcTableComponent, FlcUtilService } from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';
import { RecerptService } from '../../receipt.service';
import { OrderSelectorItem, OrderSelectorParams } from '../../models/receipt-detail.interface';
import { searchConfig, tableHeader } from './order-selector-drawer.config';

@Component({
  selector: 'flss-order-selector-drawer',
  templateUrl: './order-selector-drawer.component.html',
  styleUrls: ['./order-selector-drawer.component.scss'],
})
export class OrderSelectorDrawerComponent implements OnInit, <PERSON><PERSON><PERSON><PERSON> {
  @ViewChild('searchBarWrap', { static: true }) searchBarWrap!: ElementRef;
  @ViewChild('flcTable', { static: false }) flcTable!: FlcTableComponent;

  private destroy$ = new Subject<void>();
  searchList = searchConfig;
  searchParams: OrderSelectorParams = {};

  tableConfig = {
    tableName: 'orderSelectorTable',
    dataList: <OrderSelectorItem[]>[],
    count: 0,
    height: 500,
    loading: false,
    pageSize: 20,
    pageIndex: 1,
    translateName: '',
    detailBtn: false,
    canSetHeader: true,
    actionWidth: '80px',
    hasCheckbox: true,
    settingBtnPos: 'start',
    trCheck: false,
    hasAction: true,
    uniqueId: 'order_code',
    width: '100%',
  };

  tableHeader = tableHeader;

  optionList: { [key: string]: any[] } = {};

  selectedItems: OrderSelectorItem[] = [];
  selectedIds: string[] = [];

  // 合计数据
  summaryData = {
    totalQuantity: 0,
    totalEstimatedAmount: 0,
    totalAdvanceAmount: 0,
    totalAdvanceAmountLocal: 0,
    averageAdvanceRatio: '0.00',
  };

  // 传入参数
  customerName?: string;
  currency?: string;
  historySelectedIds: string[] = [];

  constructor(
    private recerptService: RecerptService,
    private message: NzMessageService,
    private drawerHelper: FlcDrawerHelperService,
    private drawerRef: NzDrawerRef,
    private _util: FlcUtilService
  ) {}

  ngOnInit() {
    console.log(this.customerName, this.currency, this.historySelectedIds);
    if (this.customerName) {
      this.searchParams.customer = this.customerName;
    }
    this.calcTableHeight();
    this.getOptions();
    this.loadData();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  calcTableHeight() {
    const bodyHeight = document.querySelector('.ant-drawer-body')?.clientHeight || 0;
    const searchBarHeight = this.searchBarWrap.nativeElement.clientHeight;
    const renderY = bodyHeight - searchBarHeight;
    const renderScrollY = renderY - 130;
    this.tableConfig.height = renderScrollY;
    this.tableConfig = { ...this.tableConfig };
  }
  onChangeDatePicker() {
    this.loadData();
  }
  onSearch() {
    this.searchParams = {
      ...this.searchParams,
    };
    this.tableConfig.pageIndex = 1;
    this.loadData();
  }

  onReset() {
    this.searchParams = {
      // customer: this.customerId
    };
    this.tableConfig.pageIndex = 1;
    this.loadData();
  }

  loadData() {
    this.tableConfig.loading = true;

    const params: OrderSelectorParams = {
      ...this.searchParams,
      page: this.tableConfig.pageIndex,
      size: this.tableConfig.pageSize,
      // customer: String(this.customerId)
    };
    if (params.create_time_range) {
      params.start_time = new Date(params.create_time_range[0]).setHours(0, 0, 0, 0);
      params.end_time = new Date(params.create_time_range[1]).setHours(23, 59, 59, 999);
      delete params.create_time_range;
    }

    this.recerptService
      .getOrderList(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res.code === 200) {
            this.tableConfig.dataList = res.data.list || [];
            this.tableConfig.count = res.data.total || 0;
            this.tableConfig.loading = false;
            this.tableConfig = { ...this.tableConfig };
          } else {
            this.tableConfig.dataList = [];
            this.tableConfig.count = 0;
            this.message.error(res.message || '获取数据失败');
            this.tableConfig.loading = false;
            this.tableConfig = { ...this.tableConfig };
          }
        },
        error: (error: any) => {
          console.error('获取订单列表失败:', error);
          this.message.error('获取数据失败');
          this.tableConfig.loading = false;
        },
      });
  }

  onPageIndexChange(pageIndex: number) {
    this.tableConfig.pageIndex = pageIndex;
    this.loadData();
  }

  onPageSizeChange(pageSize: number) {
    this.tableConfig.pageSize = pageSize;
    this.tableConfig.pageIndex = 1;
    this.loadData();
  }

  onSelectedChange(selectedData: any) {
    console.log(selectedData);
    this.selectedItems = selectedData.list || [];
    this.selectedIds = this.selectedItems.map((item) => item.order_uuid || '');
    // this.calculateSummary();
  }

  getOptions() {
    this.recerptService.getOrderListOptions().subscribe((res: any) => {
      if (res.code === 200) {
        this.optionList = res.data;
      }
    });
  }

  onSelectSingle(item: OrderSelectorItem) {
    // console.log(item);
    if (this.historySelectedIds.length > 0) {
      if (item.customer_name !== this.customerName || item.currency_id !== this.currency) {
        this.message.error('请选择同客户同币种的订单哦');
        return;
      }
      if (this.historySelectedIds.some((id) => id == item.order_code)) {
        this.message.error('请勿重复选择订单');
        return;
      }

      // 单选模式
      this.drawerRef.close({
        selectedItems: [item],
      });
    } else {
      // 单选模式
      this.drawerRef.close({
        selectedItems: [item],
      });
    }
  }

  onConfirmSelection() {
    if (this.selectedItems.length === 0) {
      this.message.warning('请选择订单');
      return;
    }
    if (this.historySelectedIds.length > 0) {
      if (this.selectedItems.some((item) => item.customer_name !== this.customerName || item.currency_id !== this.currency)) {
        this.message.error('请选择同客户同币种的订单哦');
        return;
      }
      // if (this.selectedItems.some((item) => this.historySelectedIds.includes(item.order_code))) {
      //   this.message.error('请勿重复选择订单');
      //   return;
      // }
      this.drawerRef.close({
        selectedItems: this.selectedItems,
      });
    } else {
      let hasSame = this.selectedItems.some((sel) => {
        if (this.selectedItems.some((item) => item.customer_name !== sel.customer_name || item.currency_id !== sel.currency_id)) {
          this.message.error('请选择同客户同币种的订单哦');
          return true;
        }
        return false;
      });
      if (hasSame) return;
      this.selectedItems = this.selectedItems.filter((item) => !this.historySelectedIds.includes(item.order_code));
      this.drawerRef.close({
        selectedItems: this.selectedItems,
      });
    }
  }

  onClearSelection() {
    this.selectedItems = [];
    this.selectedIds = [];
    this.flcTable?.clearAllSelected();
    // this.calculateSummary();
  }

  // 获取预付款占比
  _getPrepayment(row: any) {
    const ratio = row?.estimated_amount ? this._util.accDiv(row?.prepayment, row?.estimated_amount) : 0
    const percent = this._util.toFixed(this._util.accMul(ratio, 100), 2) 
    return percent ? `${row?.prepayment}(${percent}%)` : row?.prepayment
  }
}
