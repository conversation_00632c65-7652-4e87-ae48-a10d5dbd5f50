import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { TranslateModule, TranslateLoader, TranslateService } from '@ngx-translate/core';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FlcComponentsModule, FlcDirectivesModule, FlcPipesModule } from 'fl-common-lib';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { SettlementComponentsModule } from '../components/settlement-components.module';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { FlButtonModule } from 'fl-ui-angular';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { ReceivableService } from './receivable-service';
import { ReceivableRoutingModule } from './receivable-routing.module';
import { ReceivableListComponent } from './list/list.component';
import { ReceivableDetailComponent } from './detail/detail.component';
import { ReceivableManagementComponentPanelComponent } from './components/component-panel/receivable-management-component-panel.component';
import { ReceivableManagementDetailHeaderComponent } from './components/detail-header/receivable-management-detail-header.component';
import { ReceivableManagementBaseinfoComponent } from './components/baseinfo/receivable-management-baseinfo.component';
import { ReceivableManagementLoanDetailComponent } from './components/loan-detail/receivable-management-loan-detail.component';
import { ReceivableManagementAdjustmentDetailComponent } from './components/adjustment-detail/receivable-management-adjustment-detail.component';
import {OutboundSelectorDrawerComponent} from './components/outbound-selector-drawer/outbound-selector-drawer.component';

const ngModules = [
  NzSelectModule,
  NzDatePickerModule,
  NzButtonModule,
  NzFormModule,
  NzIconModule,
  NzTableModule,
  NzInputNumberModule,
  NzInputModule,
  NzDrawerModule,
  NzModalModule,
  NzPopoverModule,
  NzCheckboxModule,
  NzSpinModule,
  NzPopconfirmModule,
  NzSelectModule,
  NzIconModule,
  NzTreeSelectModule,
  NzDividerModule
];

@NgModule({
  declarations: [
    ReceivableListComponent,
    ReceivableDetailComponent,
    ReceivableManagementDetailHeaderComponent,
    ReceivableManagementBaseinfoComponent,
    ReceivableManagementLoanDetailComponent,
    ReceivableManagementAdjustmentDetailComponent,
    ReceivableManagementComponentPanelComponent,
    OutboundSelectorDrawerComponent


  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ReceivableRoutingModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/settlement/receivable/', suffix: '.json' },
            { prefix: './assets/i18n/settlement/settlement-components/', suffix: '.json' },
            { prefix: './assets/i18n/settlement/', suffix: '.json' },
            { prefix: './assets/i18n/flss-component/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
    ...ngModules,
    FlcComponentsModule,
    FlcDirectivesModule,
    FlcPipesModule,
    FlButtonModule,
    SettlementComponentsModule,
    NzResizableModule,
  ],
  providers: [
    ReceivableService,
  ],
})
export class ReceivableModule {
  constructor(public translateService: TranslateService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);
  }
}
