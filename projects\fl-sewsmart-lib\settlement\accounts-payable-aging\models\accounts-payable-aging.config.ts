
/** 列表页面搜索列表 */
export const accountsPayableAgingSearchList = [
  {
    label: '供应商',
    labelKey: 'supplier_name',
    valueKey: 'supplier_name',
  },
  {
    label: '合同号',
    labelKey: 'contract_number',
    valueKey: 'contract_number',
  },
  {
    label: '账龄天数',
    labelKey: 'age_day_list',
    valueKey: 'age_day',
  },
]?.map((item) => {
  return {...item, isShow: true}
});

/** 账龄天数 */
export const age_day_list = [
  {label: '0-30天账龄', value: '1'},
  {label: '30-60天账龄', value: '2'},
  {label: '60-90天账龄', value: '3'},
  {label: '≥90天账龄', value: '4'},
]

/** 应收查询表头 */
export const accountsPayableAgingTableHeader = [
  { label: '类型', key: 'account_type'},
  { label: '供应商', key: 'supplier_name'},
  { label: '合同号', key: 'contract_number'},
  { label: '合同金额', key: 'contract_money'},
  { label: '发票金额', key: 'invoice_money'},
  { label: '未开票金额', key: 'no_invoice_money'},
  { label: '应付日期', key: 'due_date', type: 'date'},
  { label: '未付金额', key: 'unpaid_money'},
  { label: '已付金额', key: 'paid_money'},
  { label: '0-30天账龄', key: 'receivable_age_1'},
  { label: '30-60天账龄', key: 'receivable_age_2'},
  { label: '60-90天账龄', key: 'receivable_age_3'},
  { label: '≥90天账龄', key: 'receivable_age_4'},
].map((item) => {
  return { type: 'text', width: '110px', visible: true, pinned: false, ...item}
})