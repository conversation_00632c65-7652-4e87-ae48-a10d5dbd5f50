import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { FlcDrawerHelperService, FlcTableComponent } from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';
import { RecerptService } from '../../receipt.service';
import { ReceivableSelectorItem, ReceivableSelectorParams } from '../../models/receipt-detail.interface';
import { searchConfig, tableHeader } from './receivable-selector-drawer.config';

@Component({
  selector: 'flss-receivable-selector-drawer',
  templateUrl: './receivable-selector-drawer.component.html',
  styleUrls: ['./receivable-selector-drawer.component.scss'],
})
export class ReceivableSelectorDrawerComponent implements OnInit, <PERSON><PERSON><PERSON><PERSON> {
  @ViewChild('searchBarWrap', { static: true }) searchBarWrap!: ElementRef;
  @ViewChild('flcTable', { static: false }) flcTable!: FlcTableComponent;

  private destroy$ = new Subject<void>();

  searchList = searchConfig;
  searchParams: ReceivableSelectorParams = {};

  tableConfig = {
    tableName: 'receivableSelectorTable',
    dataList: <ReceivableSelectorItem[]>[],
    count: 0,
    height: 500,
    loading: false,
    pageSize: 20,
    pageIndex: 1,
    translateName: '',
    detailBtn: false,
    canSetHeader: true,
    actionWidth: '80px',
    hasCheckbox: true,
    settingBtnPos: 'start',
    trCheck: false,
    hasAction: true,
    uniqueId: 'bills_receivable_code',
    width: '100%',
  };

  tableHeader = tableHeader;
  optionList: { [key: string]: any[] } = {};
  selectedItems: ReceivableSelectorItem[] = [];
  selectedIds: string[] = [];

  // 传入参数
  customerName?: string;
  currency?: string;
  historySelectedIds: string[] = [];

  constructor(
    private recerptService: RecerptService,
    private message: NzMessageService,
    private drawerHelper: FlcDrawerHelperService,
    private drawerRef: NzDrawerRef
  ) {}

  ngOnInit() {
    if (this.customerName) {
      this.searchParams.customer_id = this.customerName;
    }
    this.calcTableHeight();
    this.getOptions();
    this.loadData();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  calcTableHeight() {
    setTimeout(() => {
      const searchHeight = this.searchBarWrap?.nativeElement?.offsetHeight || 0;
      const headerHeight = 120; // 标题和按钮区域高度
      const paginationHeight = 60; // 分页高度
      const windowHeight = window.innerHeight;
      const drawerHeaderHeight = 55; // 抽屉头部高度

      let height = windowHeight * 0.8 - searchHeight - headerHeight - paginationHeight - drawerHeaderHeight;
      if (height < 300) {
        height = 300;
      }
      this.tableConfig.height = height;
      this.tableConfig = { ...this.tableConfig };
    }, 100);
  }
  onChangeDatePicker() {
    this.tableConfig.pageIndex = 1;
    this.loadData();
  }
  onSearch() {
    this.searchParams = {
      ...this.searchParams,
    };
    this.tableConfig.pageIndex = 1;
    this.loadData();
  }

  onReset() {
    // this.searchForm.reset();
    // this.searchParams = {
    //   page: '1',
    //   size: '20',
    //   customer_id: this.customerId
    // };
    this.searchParams = {
      // customer: this.customerId
    };
    this.tableConfig.pageIndex = 1;
    this.loadData();
  }

  loadData() {
    this.tableConfig.loading = true;

    const params: ReceivableSelectorParams = {
      ...this.searchParams,
      page: this.tableConfig.pageIndex,
      size: this.tableConfig.pageSize,
    };

    if (params.create_time_range) {
      params.create_start_time = new Date(params.create_time_range[0]).setHours(0, 0, 0, 0);
      params.create_end_time = new Date(params.create_time_range[1]).setHours(23, 59, 59, 999);
      delete params.create_time_range;
    }
    if (params.receipt_time_range) {
      params.expected_receipt_date_start = new Date(params.receipt_time_range[0]).setHours(0, 0, 0, 0);
      params.expected_receipt_date_end = new Date(params.receipt_time_range[1]).setHours(23, 59, 59, 999);
      delete params.receipt_time_range;
    }
    this.recerptService
      .getReceivableList(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res.code === 200) {
            this.tableConfig.dataList = res.data.list || [];
            this.tableConfig.count = res.data.total || 0;
            this.tableConfig.loading = false;
            this.tableConfig = { ...this.tableConfig };
          } else {
            this.message.error(res.message || '获取数据失败');
            this.tableConfig.loading = false;
          }
        },
        error: (error: any) => {
          console.error('获取应收单列表失败:', error);
          this.message.error('获取数据失败');
          this.tableConfig.loading = false;
        },
      });
  }

  onPageIndexChange(pageIndex: number) {
    this.tableConfig.pageIndex = pageIndex;
    this.loadData();
  }

  onPageSizeChange(pageSize: number) {
    this.tableConfig.pageSize = pageSize;
    this.tableConfig.pageIndex = 1;
    this.loadData();
  }

  onSelectedChange(selectedData: any) {
    console.log(selectedData);
    this.selectedItems = selectedData.list || [];
    this.selectedIds = this.selectedItems.map((item) => item.bills_receivable_id || '');
  }

  getOptions() {
    this.recerptService.getReceivableListOptions().subscribe((res: any) => {
      if (res.code === 200) {
        this.optionList = res.data;
        // if (this.customerName) {
        //   this.searchParams.customer_id = this.optionList.customers.find((item: any) => item.value === this.customerName)?.value || '';
        // }
      }
    });
  }

  onSelectSingle(item: ReceivableSelectorItem) {
    // 单选模式
    if (this.historySelectedIds.length > 0) {
      if (item.customer_id !== this.customerName || item.currency_name !== this.currency) {
        this.message.error('请选择同客户同币种的订单哦');
        return;
      }
      if (this.historySelectedIds.some((id) => id == item.bills_receivable_code)) {
        this.message.error('请勿重复选择订单');
        return;
      }

      // 单选模式
      this.drawerRef.close({
        selectedItems: [item],
      });
    } else {
      // 单选模式
      this.drawerRef.close({
        selectedItems: [item],
      });
    }
  }

  onConfirmSelection() {
    if (this.selectedItems.length === 0) {
      this.message.warning('请选择应收单');
      return;
    }

    if (this.historySelectedIds.length > 0) {
      if (this.selectedItems.some((item) => item.customer_id !== this.customerName || item.currency_name !== this.currency)) {
        this.message.error('请选择同客户同币种的订单哦');
        return;
      }
      // if (this.selectedItems.some((item) => this.historySelectedIds.includes(item.order_code))) {
      //   this.message.error('请勿重复选择订单');
      //   return;
      // }
      this.drawerRef.close({
        selectedItems: this.selectedItems,
      });
    } else {
      let hasSame = this.selectedItems.some((sel) => {
        if (this.selectedItems.some((item) => item.customer_id !== sel.customer_id || item.currency_name !== sel.currency_name)) {
          this.message.error('请选择同客户同币种的订单哦');
          return true;
        }
        return false;
      });
      if (hasSame) return;
      this.selectedItems = this.selectedItems.filter((item) => !this.historySelectedIds.includes(item.bills_receivable_code));
      this.drawerRef.close({
        selectedItems: this.selectedItems,
      });
    }
  }

  onClearSelection() {
    this.selectedItems = [];
    this.selectedIds = [];
    this.flcTable?.clearAllSelected();
  }
}
